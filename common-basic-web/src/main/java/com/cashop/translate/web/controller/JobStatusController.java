package com.cashop.translate.web.controller;

import com.cashop.translate.service.config.ProviderRouteConfig;
import com.cashop.translate.web.config.XxlJobConfig;
import com.cashop.translate.web.job.XxlJobHandler;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务状态检查控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/job")
@Tag(name = "任务状态", description = "任务状态检查接口")
public class JobStatusController {

    @Autowired
    private ProviderRouteConfig providerRouteConfig;

    @Autowired
    private ApplicationContext applicationContext;

    @GetMapping("/status")
    @Operation(summary = "获取任务状态", description = "检查当前任务配置和组件状态")
    public Map<String, Object> getJobStatus() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取当前配置
        String batchResultType = providerRouteConfig.getBatchResultType();
        result.put("batchResultType", batchResultType);
        
        // 检查 XXL-Job 相关组件是否存在
        boolean xxlJobConfigExists = applicationContext.containsBean("xxlJobConfig");
        boolean xxlJobExecutorExists = applicationContext.containsBean("xxlJobExecutor");
        boolean xxlJobHandlerExists = applicationContext.containsBean("xxlJobHandler");
        
        result.put("xxlJobConfigExists", xxlJobConfigExists);
        result.put("xxlJobExecutorExists", xxlJobExecutorExists);
        result.put("xxlJobHandlerExists", xxlJobHandlerExists);
        
        // 如果是 xxl-job 模式，检查执行器状态
        if ("xxl-job".equals(batchResultType)) {
            try {
                XxlJobSpringExecutor executor = applicationContext.getBean(XxlJobSpringExecutor.class);
                result.put("xxlJobExecutorStatus", "已创建");
                result.put("xxlJobExecutorClass", executor.getClass().getName());
            } catch (Exception e) {
                result.put("xxlJobExecutorStatus", "未找到: " + e.getMessage());
            }
            
            try {
                XxlJobHandler handler = applicationContext.getBean(XxlJobHandler.class);
                result.put("xxlJobHandlerStatus", "已创建");
                result.put("xxlJobHandlerClass", handler.getClass().getName());
            } catch (Exception e) {
                result.put("xxlJobHandlerStatus", "未找到: " + e.getMessage());
            }
        }
        
        // 检查本地任务配置
        boolean localScheduleConfigExists = applicationContext.containsBean("localScheduleConfig");
        result.put("localScheduleConfigExists", localScheduleConfigExists);
        
        return result;
    }

    @GetMapping("/config")
    @Operation(summary = "获取配置详情", description = "获取详细的配置信息")
    public Map<String, Object> getJobConfig() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取配置详情
        result.put("batchResultType", providerRouteConfig.getBatchResultType());
        result.put("syncImageSingleProviderWeight", providerRouteConfig.getSyncImageSingleProviderWeight());
        result.put("asyncImageBatchProviderWeight", providerRouteConfig.getAsyncImageBatchProviderWeight());
        result.put("syncTextSingleProviderWeight", providerRouteConfig.getSyncTextSingleProviderWeight());
        result.put("asyncTextBatchProviderWeight", providerRouteConfig.getAsyncTextBatchProviderWeight());
        
        // 获取图片配置
        if (providerRouteConfig.getImage() != null) {
            result.put("imageConfig", Collections.singletonMap(
                "batchResultType", providerRouteConfig.getImage().getBatch().getResult().getType()
            ));
        }
        
        return result;
    }
}
