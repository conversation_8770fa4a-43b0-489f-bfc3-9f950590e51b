package com.cashop.translate.dao.mapper;

import com.cashop.translate.dao.entity.TranslateRequestRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 翻译请求记录Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface TranslateRequestRecordMapper {

    /**
     * 插入记录
     */
    int insert(TranslateRequestRecord record);

    /**
     * 根据请求ID查询记录
     */
    TranslateRequestRecord selectByRequestId(@Param("requestId") String requestId);

    /**
     * 根据ID查询记录
     */
    TranslateRequestRecord selectById(@Param("id") Long id);

    /**
     * 更新记录
     */
    int updateById(TranslateRequestRecord record);

    /**
     * 更新云服务API状态和响应
     */
    int updateCloudApiStatus(@Param("id") Long id,
                           @Param("cloudApiStatus") String cloudApiStatus,
                           @Param("cloudApiResponse") String cloudApiResponse,
                           @Param("requestStatus") String requestStatus,
                           @Param("taskId") String taskId,
                           @Param("errorMessage") String errorMessage,
                           @Param("imageSyncResult") String imageSyncResult,
                           @Param("textSyncResult") String textSyncResult,
                           @Param("textBatchResults") String textBatchResults);

    /**
     * 更新异步查询结果状态和响应
     */
    int updateAsyncStatus(@Param("id") Long id,
                         @Param("cloudApiAsyncStatus") String cloudApiAsyncStatus,
                         @Param("cloudApiAsyncResponse") String cloudApiAsyncResponse,
                         @Param("requestStatus") String requestStatus,
                         @Param("retryCount") Integer retryCount,
                         @Param("errorMessage") String errorMessage,
                         @Param("imageBatchResults") String imageBatchResults);

    /**
     * 查询需要异步查询结果的记录
     */
    List<TranslateRequestRecord> selectPendingAsyncRecords(@Param("requestType") String requestType,
                                                          @Param("cloudApiStatus") String cloudApiStatus,
                                                          @Param("maxRetryCount") Integer maxRetryCount,
                                                          @Param("providerList") List<String> providerList);

    /**
     * 查询超时的任务记录
     */
    List<TranslateRequestRecord> selectTimeoutRecords(@Param("timeoutHours") Integer timeoutHours);

    /**
     * 删除记录
     */
    int deleteById(@Param("id") Long id);

    /**
     * 查询需要回调的记录
     */
    List<TranslateRequestRecord> selectPendingCallbackRecords(@Param("callbackStatus") Integer callbackStatus,
                                                              @Param("callbackMaxRetryCount") Integer callbackMaxRetryCount);

    /**
     * 更新回调状态
     */
    int updateCallbackStatus(@Param("id") Long id,
                           @Param("callbackStatus") Integer callbackStatus,
                           @Param("callbackResponse") String callbackResponse,
                           @Param("callbackRetryCount") Integer callbackRetryCount);

    /**
     * 查询待处理的异步批量文本翻译任务
     */
    List<TranslateRequestRecord> selectPendingAsyncTextBatchTasks();

    /**
     * 查询需要回调的异步批量文本翻译任务
     */
    List<TranslateRequestRecord> selectPendingAsyncTextBatchCallbackTasks();
}
