package com.cashop.translate.common.dto;

public class TranslateTextBatchResult {

    private Integer code;
    private String message;
    private Boolean success;
    private String sourceText;
    private String finalText;
    
    public TranslateTextBatchResult() {

    }

    public TranslateTextBatchResult(Integer code, String message, Boolean success, String sourceText, String finalText) {
        this.code = code;
        this.message = message;
        this.success = success;
        this.sourceText = sourceText;
        this.finalText = finalText;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getSourceText() {
        return sourceText;
    }

    public void setSourceText(String sourceText) {
        this.sourceText = sourceText;
    }

    public String getFinalText() {
        return finalText;
    }

    public void setFinalText(String finalText) {
        this.finalText = finalText;
    }

    @Override
    public String toString() {
        return "TranslateTextBatchResult{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", success=" + success +
                ", sourceText='" + sourceText + '\'' +
                ", finalText='" + finalText + '\'' +
                '}';
    }



}

