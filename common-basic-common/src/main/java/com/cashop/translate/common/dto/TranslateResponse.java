package com.cashop.translate.common.dto;

import java.util.List;

/**
 * 翻译响应DTO
 * 
 * <AUTHOR>
 */
public class TranslateResponse {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 请求状态
     */
    private String requestStatus;

    /**
     * 云服务API状态
     */
    private String cloudApiStatus;

    /**
     * 云服务API响应
     */
    private String cloudApiResponse;

    /**
     * 异步查询结果状态
     */
    private String cloudApiAsyncStatus;

    /**
     * 异步查询结果响应
     */
    private String cloudApiAsyncResponse;

    /**
     * 任务ID（批量翻译），用于后续获取翻译结果
     */
    private String taskId;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 成功标识
     */
    private boolean success;

    /**
     * 图片同步翻译结果URL
     */
    private String translateImageResult;

    /**
     * 图片异步翻译结果URL列表
     */
    private List<TranslateImageBatchResult> translateImageBatchResults;

    /**
     * 文本翻译结果
     */
    private String translateTextResult;

    /**
     * 文本批量翻译结果
     */
    private List<TranslateTextBatchResult> translateTextBatchResults;

    private Integer responseCode;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(String requestStatus) {
        this.requestStatus = requestStatus;
    }

    public String getCloudApiStatus() {
        return cloudApiStatus;
    }

    public void setCloudApiStatus(String cloudApiStatus) {
        this.cloudApiStatus = cloudApiStatus;
    }

    public String getCloudApiResponse() {
        return cloudApiResponse;
    }

    public void setCloudApiResponse(String cloudApiResponse) {
        this.cloudApiResponse = cloudApiResponse;
    }

    public String getCloudApiAsyncStatus() {
        return cloudApiAsyncStatus;
    }

    public void setCloudApiAsyncStatus(String cloudApiAsyncStatus) {
        this.cloudApiAsyncStatus = cloudApiAsyncStatus;
    }

    public String getCloudApiAsyncResponse() {
        return cloudApiAsyncResponse;
    }

    public void setCloudApiAsyncResponse(String cloudApiAsyncResponse) {
        this.cloudApiAsyncResponse = cloudApiAsyncResponse;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getTranslateImageResult() {
        return translateImageResult;
    }

    public void setTranslateImageResult(String translateImageSyncResultUrl) {
        this.translateImageResult = translateImageSyncResultUrl;
    }

    public List<TranslateImageBatchResult> getTranslateImageBatchResults() {
        return translateImageBatchResults;
    }

    public void setTranslateImageBatchResults(List<TranslateImageBatchResult> translateImageAsyncResults) {
        this.translateImageBatchResults = translateImageAsyncResults;
    }

    public String getTranslateTextResult() {
        return translateTextResult;
    }

    public void setTranslateTextResult(String translateTextResult) {
        this.translateTextResult = translateTextResult;
    }

    public List<TranslateTextBatchResult> getTranslateTextBatchResults() {
        return translateTextBatchResults;
    }

    public void setTranslateTextBatchResults(List<TranslateTextBatchResult> translateTextBatchResults) {
        this.translateTextBatchResults = translateTextBatchResults;
    }

    public Integer getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(Integer responseCode) {
        this.responseCode = responseCode;
    }
}
